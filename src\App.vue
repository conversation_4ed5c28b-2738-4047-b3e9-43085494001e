<template>
  <el-config-provider :locale="zhCn">
    <div class="app-container">
      <el-container>
        <!-- 侧边导航 -->
        <el-aside width="200px">
          <div class="logo-container">
            <h2>股票数据可视化</h2>
          </div>
          <el-menu
            router
            :default-active="$route.path"
            class="el-menu-vertical"
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
          >
            <el-menu-item index="/">
              <el-icon><House /></el-icon>
              <span>首页</span>
            </el-menu-item>
            <el-menu-item index="/kline">
              <el-icon><TrendCharts /></el-icon>
              <span>K线图</span>
            </el-menu-item>
            <el-menu-item index="/price-distribution">
              <el-icon><Histogram /></el-icon>
              <span>价格涨跌幅分布</span>
            </el-menu-item>
            <el-menu-item index="/ma-analysis">
              <el-icon><DataLine /></el-icon>
              <span>均线分析</span>
            </el-menu-item>
            <el-menu-item index="/investment-simulation">
              <el-icon><Money /></el-icon>
              <span>投资模拟</span>
            </el-menu-item>
            <el-menu-item index="/batch-stock-analysis">
              <el-icon><DataAnalysis /></el-icon>
              <span>批量股票分析</span>
            </el-menu-item>
            <el-menu-item index="/period-change-stats">
              <el-icon><GoldMedal /></el-icon>
              <span>周期涨跌统计</span>
            </el-menu-item>
            <el-menu-item index="/batch-period-change-stats">
              <el-icon><Stopwatch /></el-icon>
              <span>批量周期涨跌统计</span>
            </el-menu-item>
          </el-menu>
        </el-aside>
        
        <!-- 主内容区 -->
        <el-container>
          <el-header>
            <div class="header-title">{{ pageTitle }}</div>
          </el-header>
          <el-main>
            <router-view />
          </el-main>
          <el-footer>
            <div class="footer-content">
              <p>© 2023 股票数据可视化平台 | 基于Vue.js和ECharts</p>
            </div>
          </el-footer>
        </el-container>
      </el-container>
    </div>
  </el-config-provider>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElConfigProvider } from 'element-plus'
import { House, TrendCharts, Histogram, DataLine, Money, DataAnalysis, GoldMedal, Stopwatch } from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

// 获取路由信息
const route = useRoute()

// 页面标题
const pageTitle = computed(() => {
  switch (route.path) {
    case '/':
      return '首页'
    case '/kline':
      return 'K线图分析'
    case '/price-distribution':
      return '价格区间涨跌幅分布'
    case '/ma-analysis':
      return '均线分析'
    case '/investment-simulation':
      return '投资模拟分析'
    case '/batch-stock-analysis':
      return '批量股票分析'
    case '/period-change-stats':
      return '周期性涨跌幅统计'
    case '/batch-period-change-stats':
      return '批量周期性涨跌幅统计'
    default:
      return '股票数据可视化'
  }
})
</script>

<style scoped>
.app-container {
  height: 100vh;
  width: 100vw;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #304156;
  color: #fff;
}

.logo-container h2 {
  margin: 0;
  font-size: 18px;
}

.el-menu-vertical {
  height: calc(100vh - 60px);
  border-right: none;
}

.el-header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
}

.el-footer {
  background-color: #f5f7fa;
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
}

.footer-content {
  text-align: center;
}

.el-aside {
  background-color: #304156;
  color: #bfcbd9;
}

.el-main {
  background-color: #f0f2f5;
  padding: 20px;
  height: calc(100vh - 120px);
  overflow-y: auto;
}
</style> 